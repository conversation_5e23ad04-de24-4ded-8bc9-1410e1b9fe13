import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
  Modal,
  Animated,
  Dimensions,
  Vibration,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';
import CustomInput from '../components/CustomInput';
import GradientBackground from '../components/GradientBackground';
import { useMockAuth } from '../context/MockAuthContext';
import {
  HEALTHCARE_PROVIDERS,
  addAppointment
} from '../data/mockHealthData';

const { width, height } = Dimensions.get('window');

const BookingScreen = ({ navigation }) => {
  const { currentUser } = useMockAuth();
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('');
  const [reason, setReason] = useState('');
  const [contactNumber, setContactNumber] = useState(currentUser?.phone || '');
  const [showProviderModal, setShowProviderModal] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false);
  const [showTimeModal, setShowTimeModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Animation refs
  const modalAnimatedValue = useRef(new Animated.Value(0)).current;
  const backdropAnimatedValue = useRef(new Animated.Value(0)).current;
  const cardAnimations = useRef([]).current;

  // Available dates (next 14 days)
  const availableDates = Array.from({ length: 14 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() + i);
    return {
      fullDate: date.toLocaleDateString(),
      dayName: date.toLocaleDateString('en-US', { weekday: 'short' }),
      dayNumber: date.getDate(),
      monthName: date.toLocaleDateString('en-US', { month: 'short' }),
      year: date.getFullYear(),
      isToday: i === 0,
      dateObject: date
    };
  });

  // Available time slots
  const availableTimes = [
    '08:00 AM', '09:00 AM', '10:00 AM', '11:00 AM',
    '12:00 PM', '01:00 PM', '02:00 PM', '03:00 PM', '04:00 PM'
  ];

  // Initialize card animations
  useEffect(() => {
    cardAnimations.length = availableDates.length;
    for (let i = 0; i < availableDates.length; i++) {
      cardAnimations[i] = new Animated.Value(0);
    }
  }, []);

  // Animation functions
  const openDateModal = () => {
    setShowDateModal(true);

    // Animate backdrop
    Animated.timing(backdropAnimatedValue, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Animate modal
    Animated.spring(modalAnimatedValue, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();

    // Stagger card animations
    const cardAnimationSequence = availableDates.map((_, index) =>
      Animated.timing(cardAnimations[index], {
        toValue: 1,
        duration: 400,
        delay: index * 50,
        useNativeDriver: true,
      })
    );

    Animated.stagger(50, cardAnimationSequence).start();
  };

  const closeDateModal = () => {
    // Animate out
    Animated.parallel([
      Animated.timing(backdropAnimatedValue, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(modalAnimatedValue, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
      ...cardAnimations.map(anim =>
        Animated.timing(anim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        })
      ),
    ]).start(() => {
      setShowDateModal(false);
    });
  };

  const handleDateSelection = (dateObj) => {
    // Haptic feedback
    if (Platform.OS === 'ios') {
      Vibration.vibrate(10);
    }

    setSelectedDate(dateObj);
    closeDateModal();
  };

  // Handle booking submission
  const handleSubmit = async () => {
    // Validate form
    if (!selectedDate || !selectedTime || !selectedProvider || !reason || !contactNumber) {
      Alert.alert('Missing Information', 'Please fill in all fields to book your appointment.');
      return;
    }

    // Format phone number validation
    const phoneRegex = /^\d{10}$/;
    if (!phoneRegex.test(contactNumber.replace(/\D/g, ''))) {
      Alert.alert('Invalid Phone Number', 'Please enter a valid 10-digit phone number.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Get provider details
      const provider = HEALTHCARE_PROVIDERS.find(p => p.id === selectedProvider);

      // Create appointment data
      const appointmentData = {
        date: selectedDate.fullDate || selectedDate,
        time: selectedTime,
        provider: provider.name,
        providerSpecialty: provider.specialty,
        location: provider.location,
        reason,
        contactNumber,
        userId: currentUser?.id || 'guest',
        userName: currentUser?.name || 'Guest User',
      };

      // Add appointment to storage
      const result = await addAppointment(appointmentData);

      if (result) {
        Alert.alert(
          'Appointment Booked',
          `Your appointment with ${provider.name} has been scheduled for ${selectedDate.fullDate || selectedDate} at ${selectedTime}.`,
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('NotificationsScreen')
            }
          ]
        );
      } else {
        Alert.alert('Error', 'There was a problem booking your appointment. Please try again.');
      }
    } catch (error) {
      console.error('Error booking appointment:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render provider selection modal
  const renderProviderModal = () => (
    <Modal
      visible={showProviderModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowProviderModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Healthcare Provider</Text>
            <TouchableOpacity onPress={() => setShowProviderModal(false)}>
              <Icon name="close" size={24} color={COLORS.black} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {HEALTHCARE_PROVIDERS.map((provider) => (
              <TouchableOpacity
                key={provider.id}
                style={[
                  styles.modalItem,
                  selectedProvider === provider.id && styles.selectedModalItem
                ]}
                onPress={() => {
                  setSelectedProvider(provider.id);
                  setShowProviderModal(false);
                }}
              >
                <View>
                  <Text style={styles.providerName}>{provider.name}</Text>
                  <Text style={styles.providerSpecialty}>{provider.specialty}</Text>
                  <Text style={styles.providerLocation}>{provider.location}</Text>
                </View>
                {selectedProvider === provider.id && (
                  <Icon name="check-circle" size={24} color={COLORS.primaryGreen} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  // Render beautiful animated date selection modal
  const renderDateModal = () => {
    const backdropOpacity = backdropAnimatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    });

    const modalTranslateY = modalAnimatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [height, 0],
    });

    const modalScale = modalAnimatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0.9, 1],
    });

    return (
      <Modal
        visible={showDateModal}
        transparent={true}
        animationType="none"
        onRequestClose={closeDateModal}
      >
        {/* Animated backdrop */}
        <Animated.View
          style={[
            styles.modalOverlay,
            { opacity: backdropOpacity }
          ]}
        >
          <TouchableOpacity
            style={StyleSheet.absoluteFill}
            onPress={closeDateModal}
            activeOpacity={1}
          />
        </Animated.View>

        {/* Animated modal container */}
        <Animated.View
          style={[
            styles.modalContainer,
            {
              transform: [
                { translateY: modalTranslateY },
                { scale: modalScale }
              ]
            }
          ]}
        >
          {/* Gradient header */}
          <GradientBackground
            colors={[COLORS.primaryGreen, COLORS.primaryLightGreen]}
            style={styles.modalHeader}
          >
            <Text style={styles.modalTitle}>Choose Your Date</Text>
            <Text style={styles.modalSubtitle}>Select your preferred appointment date</Text>
            <TouchableOpacity
              onPress={closeDateModal}
              style={styles.closeButton}
            >
              <Icon name="close" size={24} color={COLORS.white} />
            </TouchableOpacity>
          </GradientBackground>

          <ScrollView
            style={styles.modalContent}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            <View style={styles.dateCardsContainer}>
              {availableDates.map((dateObj, index) => {
                const cardScale = cardAnimations[index]?.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }) || new Animated.Value(1);

                const cardOpacity = cardAnimations[index]?.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 1],
                }) || new Animated.Value(1);

                const cardTranslateY = cardAnimations[index]?.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0],
                }) || new Animated.Value(0);

                const isSelected = selectedDate?.fullDate === dateObj.fullDate;

                return (
                  <Animated.View
                    key={index}
                    style={[
                      styles.animatedCardWrapper,
                      {
                        opacity: cardOpacity,
                        transform: [
                          { scale: cardScale },
                          { translateY: cardTranslateY }
                        ]
                      }
                    ]}
                  >
                    <TouchableOpacity
                      style={[
                        styles.dateCard,
                        isSelected && styles.selectedDateCard,
                        dateObj.isToday && styles.todayDateCard
                      ]}
                      onPress={() => handleDateSelection(dateObj)}
                      activeOpacity={0.8}
                    >
                      {/* Gradient overlay for selected state */}
                      {isSelected && (
                        <GradientBackground
                          colors={[COLORS.primaryGreen, COLORS.primaryLightGreen]}
                          style={StyleSheet.absoluteFill}
                        />
                      )}

                      <View style={styles.dateCardContent}>
                        <Text style={[
                          styles.dayNameText,
                          isSelected && styles.selectedDateText,
                          dateObj.isToday && !isSelected && styles.todayText
                        ]}>
                          {dateObj.dayName.toUpperCase()}
                        </Text>
                        <Text style={[
                          styles.dayNumberText,
                          isSelected && styles.selectedDateText,
                          dateObj.isToday && !isSelected && styles.todayText
                        ]}>
                          {dateObj.dayNumber}
                        </Text>
                        <Text style={[
                          styles.monthNameText,
                          isSelected && styles.selectedDateText,
                          dateObj.isToday && !isSelected && styles.todayText
                        ]}>
                          {dateObj.monthName}
                        </Text>

                        {/* Today badge */}
                        {dateObj.isToday && !isSelected && (
                          <View style={styles.todayBadge}>
                            <Text style={styles.todayBadgeText}>TODAY</Text>
                          </View>
                        )}

                        {/* Selection indicator */}
                        {isSelected && (
                          <View style={styles.selectedIcon}>
                            <Icon name="check-circle" size={22} color={COLORS.white} />
                          </View>
                        )}

                        {/* Shimmer effect for today */}
                        {dateObj.isToday && !isSelected && (
                          <View style={styles.shimmerOverlay} />
                        )}
                      </View>
                    </TouchableOpacity>
                  </Animated.View>
                );
              })}
            </View>
          </ScrollView>
        </Animated.View>
      </Modal>
    );
  };

  // Time picker state
  const [selectedHour, setSelectedHour] = useState(9);
  const [selectedMinute, setSelectedMinute] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState('AM');

  // Generate hours and minutes
  const hours = Array.from({ length: 12 }, (_, i) => i + 1);
  const minutes = [0, 15, 30, 45];

  // Format time for display
  const formatSelectedTime = () => {
    const hour = selectedHour.toString().padStart(2, '0');
    const minute = selectedMinute.toString().padStart(2, '0');
    return `${hour}:${minute} ${selectedPeriod}`;
  };

  // Popup time picker modal
  const renderTimeModal = () => (
    <Modal
      visible={showTimeModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowTimeModal(false)}
    >
      <View style={styles.popupOverlay}>
        <View style={styles.popupContainer}>
          {/* Header */}
          <View style={styles.popupHeader}>
            <Text style={styles.popupTitle}>Select Time</Text>
            <TouchableOpacity
              onPress={() => setShowTimeModal(false)}
              style={styles.popupCloseButton}
            >
              <Icon name="close" size={20} color={COLORS.gray} />
            </TouchableOpacity>
          </View>

          {/* Time Display */}
          <View style={styles.timeDisplay}>
            <Text style={styles.timeDisplayText}>{formatSelectedTime()}</Text>
          </View>

          {/* Time Controls */}
          <View style={styles.timeControls}>
            {/* Hour Selector */}
            <View style={styles.timeSection}>
              <Text style={styles.timeSectionLabel}>Hour</Text>
              <View style={styles.timeButtons}>
                <TouchableOpacity
                  style={styles.timeButton}
                  onPress={() => setSelectedHour(selectedHour > 1 ? selectedHour - 1 : 12)}
                >
                  <Icon name="minus" size={16} color={COLORS.primaryGreen} />
                </TouchableOpacity>
                <Text style={styles.timeValue}>{selectedHour}</Text>
                <TouchableOpacity
                  style={styles.timeButton}
                  onPress={() => setSelectedHour(selectedHour < 12 ? selectedHour + 1 : 1)}
                >
                  <Icon name="plus" size={16} color={COLORS.primaryGreen} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Minute Selector */}
            <View style={styles.timeSection}>
              <Text style={styles.timeSectionLabel}>Minute</Text>
              <View style={styles.minuteOptions}>
                {minutes.map((minute) => (
                  <TouchableOpacity
                    key={minute}
                    style={[
                      styles.minuteButton,
                      selectedMinute === minute && styles.selectedMinuteButton
                    ]}
                    onPress={() => setSelectedMinute(minute)}
                  >
                    <Text style={[
                      styles.minuteButtonText,
                      selectedMinute === minute && styles.selectedMinuteButtonText
                    ]}>
                      {minute.toString().padStart(2, '0')}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* AM/PM Selector */}
            <View style={styles.timeSection}>
              <Text style={styles.timeSectionLabel}>Period</Text>
              <View style={styles.periodButtons}>
                <TouchableOpacity
                  style={[
                    styles.periodButton,
                    selectedPeriod === 'AM' && styles.selectedPeriodButton
                  ]}
                  onPress={() => setSelectedPeriod('AM')}
                >
                  <Text style={[
                    styles.periodButtonText,
                    selectedPeriod === 'AM' && styles.selectedPeriodButtonText
                  ]}>
                    AM
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.periodButton,
                    selectedPeriod === 'PM' && styles.selectedPeriodButton
                  ]}
                  onPress={() => setSelectedPeriod('PM')}
                >
                  <Text style={[
                    styles.periodButtonText,
                    selectedPeriod === 'PM' && styles.selectedPeriodButtonText
                  ]}>
                    PM
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.popupActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowTimeModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={() => {
                setSelectedTime(formatSelectedTime());
                setShowTimeModal(false);
              }}
            >
              <Text style={styles.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Book Appointment</Text>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.formContainer}>
          {/* Provider Selection */}
          <Text style={styles.inputLabel}>Healthcare Provider</Text>
          <TouchableOpacity
            style={styles.selectionButton}
            onPress={() => setShowProviderModal(true)}
          >
            <Text style={[
              styles.selectionText,
              !selectedProvider && styles.placeholderText
            ]}>
              {selectedProvider
                ? HEALTHCARE_PROVIDERS.find(p => p.id === selectedProvider)?.name
                : 'Select a healthcare provider'}
            </Text>
            <Icon name="chevron-down" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          {/* Date Selection */}
          <Text style={styles.inputLabel}>Appointment Date</Text>
          <TouchableOpacity
            style={styles.selectionButton}
            onPress={openDateModal}
          >
            <Text style={[
              styles.selectionText,
              !selectedDate && styles.placeholderText
            ]}>
              {selectedDate ? `${selectedDate.dayName}, ${selectedDate.monthName} ${selectedDate.dayNumber}` : 'Select a date'}
            </Text>
            <Icon name="calendar" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          {/* Time Selection */}
          <Text style={styles.inputLabel}>Appointment Time</Text>
          <TouchableOpacity
            style={styles.selectionButton}
            onPress={() => setShowTimeModal(true)}
          >
            <Text style={[
              styles.selectionText,
              !selectedTime && styles.placeholderText
            ]}>
              {selectedTime || 'Select a time'}
            </Text>
            <Icon name="clock-outline" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          {/* Reason for Visit */}
          <Text style={styles.inputLabel}>Reason for Visit</Text>
          <CustomInput
            placeholder="Briefly describe your reason for the visit"
            value={reason}
            onChangeText={setReason}
            multiline={true}
            numberOfLines={3}
            style={styles.reasonInput}
          />

          {/* Contact Number */}
          <Text style={styles.inputLabel}>Contact Number</Text>
          <CustomInput
            placeholder="Enter your contact number"
            value={contactNumber}
            onChangeText={setContactNumber}
            keyboardType="phone-pad"
          />

          {/* Submit Button */}
          <CustomButton
            title="Book Appointment"
            onPress={handleSubmit}
            loading={isSubmitting}
            style={styles.submitButton}
          />
        </View>
      </ScrollView>

      {/* Modals */}
      {renderProviderModal()}
      {renderDateModal()}
      {renderTimeModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  scrollView: {
    padding: SPACING.md,
  },
  formContainer: {
    marginBottom: SPACING.xl,
  },
  inputLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
    marginTop: SPACING.md,
  },
  selectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 10,
    padding: SPACING.md,
    backgroundColor: COLORS.white,
  },
  selectionText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  placeholderText: {
    color: COLORS.gray,
  },
  reasonInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: SPACING.xl,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 24,
    width: '100%',
    maxHeight: height * 0.8,
    overflow: 'hidden',
    // Enhanced shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  modalHeader: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
    position: 'relative',
  },
  modalTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  modalSubtitle: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontWeight: FONT_WEIGHTS.medium,
  },
  closeButton: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  selectedModalItem: {
    backgroundColor: COLORS.offWhite,
  },
  providerName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  providerSpecialty: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginBottom: 2,
  },
  providerLocation: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  dateText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  timeText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  // Date card styles
  dateCardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  animatedCardWrapper: {
    width: '30%',
    marginBottom: SPACING.lg,
  },
  dateCard: {
    aspectRatio: 0.85,
    backgroundColor: COLORS.white,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    overflow: 'hidden',
    // Beautiful shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 6,
  },
  selectedDateCard: {
    borderColor: COLORS.primaryGreen,
    borderWidth: 2,
    // Enhanced shadow for selected state
    shadowColor: COLORS.primaryGreen,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 10,
  },
  todayDateCard: {
    borderColor: COLORS.primaryLightGreen,
    borderWidth: 2,
    // Subtle glow effect
    shadowColor: COLORS.primaryLightGreen,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  dateCardContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.sm,
    position: 'relative',
    zIndex: 2,
  },
  dayNameText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.gray,
    letterSpacing: 1,
    marginBottom: SPACING.xs,
  },
  dayNumberText: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: FONT_WEIGHTS.extraBold,
    color: COLORS.black,
    marginVertical: SPACING.xs,
    lineHeight: FONT_SIZES.xxxl + 2,
  },
  monthNameText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
    marginTop: SPACING.xs,
  },
  selectedDateText: {
    color: COLORS.white,
  },
  todayText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.bold,
  },
  todayBadge: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    backgroundColor: COLORS.primaryLightGreen,
    borderRadius: 12,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    // Badge shadow
    shadowColor: COLORS.primaryGreen,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  todayBadgeText: {
    fontSize: FONT_SIZES.xs - 1,
    fontWeight: FONT_WEIGHTS.extraBold,
    color: COLORS.white,
    letterSpacing: 0.5,
  },
  selectedIcon: {
    position: 'absolute',
    top: SPACING.sm,
    left: SPACING.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    borderRadius: 15,
    padding: SPACING.xs,
    // Icon shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
    borderRadius: 20,
  },
  // Popup time picker styles
  popupOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  popupContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    width: '100%',
    maxWidth: 350,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  popupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  popupTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  popupCloseButton: {
    padding: SPACING.xs,
  },
  timeDisplay: {
    alignItems: 'center',
    padding: SPACING.lg,
    backgroundColor: COLORS.offWhite,
  },
  timeDisplayText: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  timeControls: {
    padding: SPACING.lg,
  },
  timeSection: {
    marginBottom: SPACING.lg,
  },
  timeSectionLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  timeButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.offWhite,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: SPACING.lg,
  },
  timeValue: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    minWidth: 40,
    textAlign: 'center',
  },
  minuteOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  minuteButton: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 8,
    backgroundColor: COLORS.offWhite,
  },
  selectedMinuteButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  minuteButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  selectedMinuteButtonText: {
    color: COLORS.white,
  },
  periodButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  periodButton: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: 8,
    backgroundColor: COLORS.offWhite,
    marginHorizontal: SPACING.sm,
  },
  selectedPeriodButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  periodButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
  },
  selectedPeriodButtonText: {
    color: COLORS.white,
  },
  popupActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    marginRight: SPACING.sm,
    borderRadius: 8,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
  },
  confirmButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    marginLeft: SPACING.sm,
    borderRadius: 8,
    backgroundColor: COLORS.primaryGreen,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.white,
  },
});

export default BookingScreen;
