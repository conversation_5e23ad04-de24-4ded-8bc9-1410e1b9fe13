import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

// Mock data for government services
const GOVERNMENT_SERVICES = [
  {
    id: '1',
    title: 'ID Applications',
    description: 'Apply for new ID or replace lost ID',
    icon: '🪪',
    category: 'identity',
  },
  {
    id: '2',
    title: 'Birth Certificates',
    description: 'Register births and get certificates',
    icon: '👶',
    category: 'identity',
  },
  {
    id: '3',
    title: 'Social Grants',
    description: 'Apply for and manage social grants',
    icon: '💰',
    category: 'financial',
  },
  {
    id: '4',
    title: 'Business Registration',
    description: 'Register new businesses and companies',
    icon: '🏢',
    category: 'business',
  },
  {
    id: '5',
    title: 'Vehicle Licensing',
    description: 'Register vehicles and renew licenses',
    icon: '🚗',
    category: 'transport',
  },
  {
    id: '6',
    title: 'Tax Services',
    description: 'File tax returns and get tax clearance',
    icon: '📝',
    category: 'financial',
  },
  {
    id: '7',
    title: 'Health Services',
    description: 'Book appointments and access health services',
    icon: '🏥',
    category: 'health',
  },
];

// Service categories
const SERVICE_CATEGORIES = [
  { id: 'all', name: 'All Services', icon: '🏛️' },
  { id: 'identity', name: 'Identity', icon: '🪪' },
  { id: 'financial', name: 'Financial', icon: '💰' },
  { id: 'business', name: 'Business', icon: '🏢' },
  { id: 'transport', name: 'Transport', icon: '🚗' },
  { id: 'health', name: 'Health', icon: '🏥' },
];

const ServicesScreen = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = React.useState('all');

  // Filter services based on selected category
  const filteredServices = selectedCategory === 'all'
    ? GOVERNMENT_SERVICES
    : GOVERNMENT_SERVICES.filter(service => service.category === selectedCategory);

  // Render service category item
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.id && styles.selectedCategoryItem,
      ]}
      onPress={() => setSelectedCategory(item.id)}
    >
      <Text style={styles.categoryIcon}>{item.icon}</Text>
      <Text
        style={[
          styles.categoryName,
          selectedCategory === item.id && styles.selectedCategoryName,
        ]}
      >
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  // Render service item
  const renderServiceItem = ({ item }) => (
    <TouchableOpacity
      style={styles.serviceItem}
      onPress={() => {
        if (item.id === '7') {
          // Navigate to Health Services screen
          navigation.navigate('HealthServices');
        } else {
          // For other services, show alert
          alert(`${item.title} service details coming soon!`);
        }
      }}
    >
      <View style={styles.serviceIconContainer}>
        <Text style={styles.serviceIcon}>{item.icon}</Text>
      </View>
      <View style={styles.serviceContent}>
        <Text style={styles.serviceTitle}>{item.title}</Text>
        <Text style={styles.serviceDescription}>{item.description}</Text>
      </View>
      <Text style={styles.serviceArrow}>›</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Government Services</Text>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Service Categories */}
        <View style={styles.categoriesContainer}>
          <FlatList
            data={SERVICE_CATEGORIES}
            renderItem={renderCategoryItem}
            keyExtractor={item => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        {/* Search Bar Placeholder */}
        <TouchableOpacity
          style={styles.searchBar}
          onPress={() => alert('Search functionality coming soon!')}
        >
          <Text style={styles.searchPlaceholder}>🔍 Search for services...</Text>
        </TouchableOpacity>

        {/* Services List */}
        <View style={styles.servicesContainer}>
          <Text style={styles.sectionTitle}>
            {selectedCategory === 'all' ? 'All Services' : SERVICE_CATEGORIES.find(cat => cat.id === selectedCategory).name}
          </Text>
          <FlatList
            data={filteredServices}
            renderItem={renderServiceItem}
            keyExtractor={item => item.id}
            scrollEnabled={false}
            contentContainerStyle={styles.servicesList}
          />
        </View>

        {/* Need Help Section */}
        <View style={styles.helpContainer}>
          <Text style={styles.helpTitle}>Need Help?</Text>
          <Text style={styles.helpText}>
            Not sure which service you need? Chat with our Thusong AI assistant for guidance.
          </Text>
          <CustomButton
            title="Chat with Thusong AI"
            onPress={() => navigation.navigate('ThusongAI')}
            style={styles.chatButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.md,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  scrollView: {
    padding: SPACING.md,
    paddingBottom: 100, // Extra padding at bottom for tab bar
  },
  categoriesContainer: {
    marginBottom: SPACING.md,
  },
  categoriesList: {
    paddingVertical: SPACING.sm,
  },
  categoryItem: {
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.sm,
    marginRight: SPACING.sm,
    minWidth: 80,
  },
  selectedCategoryItem: {
    backgroundColor: COLORS.primaryGreen,
  },
  categoryIcon: {
    fontSize: FONT_SIZES.xl,
    marginBottom: SPACING.xs,
  },
  categoryName: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.black,
    textAlign: 'center',
  },
  selectedCategoryName: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.bold,
  },
  searchBar: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchPlaceholder: {
    color: COLORS.gray,
    fontSize: FONT_SIZES.md,
  },
  servicesContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
  },
  servicesList: {
    paddingBottom: SPACING.sm,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  serviceIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.offWhite,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  serviceIcon: {
    fontSize: FONT_SIZES.xl,
  },
  serviceContent: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  serviceDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  serviceArrow: {
    fontSize: FONT_SIZES.xxl,
    color: COLORS.gray,
    marginLeft: SPACING.sm,
  },
  helpContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    alignItems: 'center',
  },
  helpTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  helpText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  chatButton: {
    backgroundColor: COLORS.primaryDarkGreen,
  },
});

export default ServicesScreen;
