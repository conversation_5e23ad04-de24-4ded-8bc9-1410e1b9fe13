import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';
import {
  HEALTH_SERVICE_CATEGORIES,
  HEALTH_SERVICES,
  initializeMockHealthData
} from '../data/mockHealthData';

const HealthServicesScreen = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState('appointments');
  const [isLoading, setIsLoading] = useState(true);

  // Initialize mock data on component mount
  useEffect(() => {
    const setupData = async () => {
      await initializeMockHealthData();
      setIsLoading(false);
    };

    setupData();
  }, []);

  // Filter services based on selected category
  const filteredServices = selectedCategory === 'all'
    ? HEALTH_SERVICES
    : HEALTH_SERVICES.filter(service => service.category === selectedCategory);

  // Handle service selection
  const handleServicePress = (service) => {
    if (service.screen) {
      navigation.navigate(service.screen);
    } else {
      Alert.alert('Coming Soon', 'This feature is coming soon!');
    }
  };

  // Render service category item
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.id && styles.selectedCategoryItem,
      ]}
      onPress={() => setSelectedCategory(item.id)}
    >
      <Text style={styles.categoryIcon}>{item.icon}</Text>
      <Text
        style={[
          styles.categoryName,
          selectedCategory === item.id && styles.selectedCategoryName,
        ]}
      >
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  // Render service item
  const renderServiceItem = ({ item }) => (
    <TouchableOpacity
      style={styles.serviceItem}
      onPress={() => handleServicePress(item)}
    >
      <View style={styles.serviceIconContainer}>
        <Text style={styles.serviceIcon}>{item.icon}</Text>
      </View>
      <View style={styles.serviceContent}>
        <Text style={styles.serviceTitle}>{item.title}</Text>
        <Text style={styles.serviceDescription}>{item.description}</Text>
      </View>
      <Text style={styles.serviceArrow}>›</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Health Services</Text>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}>

        {/* Health Services Banner */}
        <View style={styles.bannerContainer}>
          <View style={styles.bannerContent}>
            <Text style={styles.bannerTitle}>Healthcare at your fingertips</Text>
            <Text style={styles.bannerSubtitle}>
              Book appointments, manage prescriptions, and more
            </Text>
          </View>
          <View style={styles.bannerIconContainer}>
            <Text style={styles.bannerIcon}>🏥</Text>
          </View>
        </View>

        {/* Service Categories */}
        <View style={styles.categoriesContainer}>
          <FlatList
            data={HEALTH_SERVICE_CATEGORIES}
            renderItem={renderCategoryItem}
            keyExtractor={item => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        {/* Search Bar Placeholder */}
        <TouchableOpacity
          style={styles.searchBar}
          onPress={() => Alert.alert('Search', 'Search functionality coming soon!')}
        >
          <Text style={styles.searchPlaceholder}>🔍 Search for health services...</Text>
        </TouchableOpacity>

        {/* Services List */}
        <View style={styles.servicesContainer}>
          <Text style={styles.sectionTitle}>
            {HEALTH_SERVICE_CATEGORIES.find(cat => cat.id === selectedCategory)?.name || 'All Services'}
          </Text>
          <FlatList
            data={filteredServices}
            renderItem={renderServiceItem}
            keyExtractor={item => item.id}
            scrollEnabled={false}
            contentContainerStyle={styles.servicesList}
          />
        </View>

        {/* Emergency Section */}
        <View style={styles.emergencyContainer}>
          <Text style={styles.emergencyTitle}>Emergency?</Text>
          <Text style={styles.emergencyText}>
            In case of a medical emergency, call the national emergency number immediately.
          </Text>
          <CustomButton
            title="Call Emergency Services"
            type="secondary"
            onPress={() => Alert.alert('Emergency', 'This would dial emergency services in a real app.')}
            style={styles.emergencyButton}
            textStyle={styles.emergencyButtonText}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  scrollView: {
    paddingBottom: SPACING.xxl,
  },
  bannerContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.primaryLightGreen,
    borderRadius: 15,
    padding: SPACING.md,
    margin: SPACING.md,
    alignItems: 'center',
  },
  bannerContent: {
    flex: 3,
  },
  bannerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  bannerSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
  },
  bannerIconContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bannerIcon: {
    fontSize: 40,
  },
  categoriesContainer: {
    marginVertical: SPACING.md,
  },
  categoriesList: {
    paddingHorizontal: SPACING.md,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: SPACING.md,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 20,
    backgroundColor: COLORS.offWhite,
  },
  selectedCategoryItem: {
    backgroundColor: COLORS.primaryGreen,
  },
  categoryIcon: {
    fontSize: FONT_SIZES.xl,
    marginBottom: SPACING.xs,
  },
  categoryName: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  selectedCategoryName: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  searchBar: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchPlaceholder: {
    color: COLORS.gray,
    fontSize: FONT_SIZES.md,
  },
  servicesContainer: {
    paddingHorizontal: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
  },
  servicesList: {
    paddingBottom: SPACING.sm,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  serviceIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.offWhite,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  serviceIcon: {
    fontSize: FONT_SIZES.xl,
  },
  serviceContent: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  serviceDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  serviceArrow: {
    fontSize: FONT_SIZES.xxl,
    color: COLORS.gray,
    marginLeft: SPACING.sm,
  },
  emergencyContainer: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 15,
    padding: SPACING.md,
    margin: SPACING.md,
    marginTop: SPACING.xl,
  },
  emergencyTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.error,
    marginBottom: SPACING.xs,
  },
  emergencyText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.md,
  },
  emergencyButton: {
    backgroundColor: COLORS.error,
    width: '100%',
  },
  emergencyButtonText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.bold,
  },
});

export default HealthServicesScreen;
